'use client';

import React, { useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { blogPosts } from '@/data/blogPosts';
import { ArrowRight, Flower2, Sunrise, Heart, MapPin, MessageSquare, BookOpen, Instagram, Facebook, CalendarCheck } from 'lucide-react';

// WELLNESS COMPONENTS - uproszczone wersje według briefu
import WellnessProvider from '@/components/WorldClassDesign/WellnessProvider';
import OpticalTypography from '@/components/WorldClassDesign/OpticalTypography';
import TestimonialSlider from '@/components/TestimonialSlider';
import FAQAccordion from '@/components/FAQAccordion';
import RetreatCalendar from '@/components/RetreatCalendar';

// SafeIcon component for rendering icons with fallback
const SafeIcon = React.memo(({ Icon, className }) => {
  if (!Icon) return null;
  return <Icon className={className} />;
});
SafeIcon.displayName = 'SafeIcon';

// Simple fade-in animation hook
const useFadeIn = (delay = 0) => {
  const [isVisible, setIsVisible] = React.useState(false);
  
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);
    
    return () => clearTimeout(timer);
  }, [delay]);
  
  return isVisible;
};

// Balijska animacja lotosu
const LotusAnimation = React.memo(() => {
  const [isAnimating, setIsAnimating] = React.useState(false);
  
  React.useEffect(() => {
    const timer = setTimeout(() => setIsAnimating(true), 1000);
    return () => clearTimeout(timer);
  }, []);
  
  return (
    <div className={`inline-block ml-4 transition-all duration-1000 ${isAnimating ? 'animate-pulse' : ''}`}>
      <span className="text-2xl text-white/70 drop-shadow-lg">🪷</span>
    </div>
  );
});
LotusAnimation.displayName = 'LotusAnimation';

// Spadające płatki (bardzo subtelne)
const FloatingPetals = React.memo(() => {
  const [petals, setPetals] = React.useState([]);
  
  React.useEffect(() => {
    const timer = setInterval(() => {
      setPetals(prev => {
        const newPetal = {
          id: Date.now(),
          x: Math.random() * 100,
          delay: Math.random() * 2,
          duration: 8 + Math.random() * 4,
          opacity: 0.1 + Math.random() * 0.1
        };
        
        // Maksymalnie 2 płatki na ekranie
        const updated = prev.length < 2 ? [...prev, newPetal] : prev;
        
        // Usuń stare płatki
        setTimeout(() => {
          setPetals(current => current.filter(p => p.id !== newPetal.id));
        }, newPetal.duration * 1000);
        
        return updated;
      });
    }, 6000);
    
    return () => clearInterval(timer);
  }, []);
  
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {petals.map(petal => (
        <div
          key={petal.id}
          className="absolute text-2xl"
          style={{
            left: `${petal.x}%`,
            opacity: petal.opacity,
            animation: `fall ${petal.duration}s linear ${petal.delay}s forwards`
          }}
        >
          🌸
        </div>
      ))}
    </div>
  );
});
FloatingPetals.displayName = 'FloatingPetals';

// Arcydzieło Hero - PRAWDZIWE BALI
const HeroSection = React.memo(() => {
  const isVisible = useFadeIn(100);
  
  const scrollToCombinedSection = useCallback(() => {
    document.getElementById('journey-inspiration')?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  }, []);

  return (
    <section className="relative h-[85vh] flex items-center justify-center w-full overflow-hidden">
      <div className="absolute inset-0 w-full h-full">
        <div className="relative w-full h-full overflow-hidden">
          <Image
            src="/images/background/bali-hero.webp"
            alt="Bali - spokojny krajobraz"
            fill
            priority
            className="object-cover"
            sizes="100vw"
            style={{
              filter: 'contrast(0.95) brightness(1.1) saturate(1.1)',
            }}
          />
        </div>
        
        {/* Balijski gradient - tylko u góry */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-transparent" 
             style={{
               background: 'linear-gradient(180deg, rgba(0,0,0,0.3) 0%, transparent 30%)'
             }} />
      </div>
      
      {/* Spadające płatki */}
      <FloatingPetals />
      
      {/* Tekst BEZPOŚREDNIO na zdjęciu */}
      <div className={`relative z-10 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-5'}`}>
        <div className="flex items-center justify-center mb-8">
          <OpticalTypography
            variant="display"
            opticalAlign={true}
            manualKerning={true}
            preventOrphans={true}
            className="text-white"
            style={{
              textShadow: '0 4px 8px rgba(0, 0, 0, 0.3), 0 2px 4px rgba(0, 0, 0, 0.2)',
              color: 'white',
              fontSize: 'clamp(2.5rem, 5vw, 4rem)',
              letterSpacing: '0.1em'
            }}
          >
            BALI YOGA JOURNEY
          </OpticalTypography>
          <LotusAnimation />
        </div>
        
        <OpticalTypography
          variant="body"
          preventOrphans={true}
          className="text-white max-w-2xl mx-auto mb-12"
          style={{
            opacity: 0.95,
            textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
            color: 'white',
            fontSize: 'clamp(1.1rem, 2vw, 1.25rem)',
            letterSpacing: '0.05em'
          }}
        >
          Harmonia ducha i tropikalnej przygody
        </OpticalTypography>
        
        {/* Złoty przycisk w stylu balijskim */}
        <button
          onClick={scrollToCombinedSection}
          className="inline-flex items-center px-10 py-5 bg-transparent text-white border-2 border-amber-200/80 text-sm font-light tracking-widest uppercase transition-all duration-300 hover:bg-amber-200/10 hover:border-amber-200 hover:shadow-lg cursor-lotus"
          style={{
            borderRadius: '0',
            textShadow: '0 1px 2px rgba(0, 0, 0, 0.2)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
          }}
          aria-label="Odkryj podróż jogową na Bali"
        >
          <span>ODKRYJ RETREATY</span>
        </button>
      </div>
      
      {/* Balijska strzałka "Przewiń w dół" */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center cursor-pointer group" onClick={scrollToCombinedSection}>
        <div className="text-white/80 text-sm font-light tracking-wide mb-2 group-hover:text-white transition-colors">
          Przewiń w dół
        </div>
        <div className="text-white/70 text-2xl group-hover:text-white transition-all duration-300 group-hover:translate-y-1">
          ⬇
        </div>
      </div>
      
      {/* Dodaj style animacji płatków */}
      <style jsx>{`
        @keyframes fall {
          0% {
            transform: translateY(-100vh) rotate(0deg);
          }
          100% {
            transform: translateY(100vh) rotate(360deg);
          }
        }
      `}</style>
    </section>
  );
});
HeroSection.displayName = 'HeroSection';

// Balijskie karty retreatów - ARCYDZIEŁO
const Card = React.memo(({ type, title, description, link, icon: Icon, index, imageUrl, id }) => {
  const isVisible = useFadeIn(index * 150);
  const isHighlight = type === 'highlight';
  const retreatIcon = getRetreatIcon(id || '');

  return (
    <div className={`transition-all duration-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-5'}`}>
      <div className="bg-gradient-to-br from-orange-50/80 via-white/80 to-emerald-50/80 backdrop-blur-sm border border-amber-200/30 hover:border-amber-400/70 transition-all duration-300 flex flex-col h-full overflow-hidden group"
           style={{
             boxShadow: '0 2px 8px rgba(139, 69, 19, 0.08), 0 4px 16px rgba(139, 69, 19, 0.04)',
           }}
      >
        {isHighlight ? (
          <div className="p-8 flex flex-col flex-grow relative">
            {/* Subtelny wzór mandali w tle */}
            <div className="absolute inset-0 opacity-[0.02] bg-gradient-to-br from-amber-100 to-transparent" />
            
            <div className="flex items-center gap-3 mb-4 relative z-10">
              <SafeIcon Icon={Icon} className="w-5 h-5 text-amber-700" />
              <span className="text-lg mr-2">{retreatIcon}</span>
              <OpticalTypography
                variant="h3"
                className="text-gray-800 font-light"
                preventOrphans={true}
              >
                {title}
              </OpticalTypography>
            </div>
            <OpticalTypography
              variant="body"
              className="text-gray-600 leading-relaxed flex-grow font-light relative z-10"
              preventOrphans={true}
            >
              {description}
            </OpticalTypography>
          </div>
        ) : (
          <>
            <div className="relative w-full h-[240px] overflow-hidden">
              <div className="relative w-full h-full">
                <Image
                  src={imageUrl || '/images/placeholder/image.jpg'}
                  alt={title}
                  fill
                  className="object-cover transition-transform duration-700 group-hover:scale-105"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
                
                {/* Ikona tematyczna w rogu */}
                <div className={`absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-sm ${id && id.includes('gili') ? 'gentle-wave' : ''}`}>
                  <span className="text-lg">{retreatIcon}</span>
                </div>
              </div>
            </div>
            <div className="p-6 flex flex-col flex-grow">
              <OpticalTypography
                variant="h3"
                className="text-gray-800 mb-3 font-light"
                preventOrphans={true}
              >
                <Link href={link} className="hover:text-amber-700 transition-colors">
                  {title}
                </Link>
              </OpticalTypography>
              <OpticalTypography
                variant="body"
                className="text-gray-600 leading-relaxed mb-4 flex-grow line-clamp-3 font-light"
                preventOrphans={true}
              >
                {description}
              </OpticalTypography>
              <button
                onClick={() => window.location.href = link}
                className="inline-flex items-center text-xs font-light text-amber-700 hover:text-amber-800 transition-colors group mt-auto self-start"
                aria-label={`Przeczytaj więcej o ${title}`}
              >
                Czytaj dalej
                <SafeIcon Icon={ArrowRight} className="ml-1 h-3 w-3 group-hover:translate-x-0.5 transition-transform" />
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
});
Card.displayName = 'Card';

// Balijskie separatory - Om i Lotus
const BalineseSeparator = React.memo(({ type = 'lotus' }) => {
  const symbols = {
    lotus: '❀',
    om: 'ॐ',
    flower: '🌺',
    mandala: '🪷'
  };
  
  const animationClass = type === 'om' ? 'breathing-om' : 'breathing-lotus';
  
  return (
    <div className="text-center py-16">
      <div className={`inline-block text-4xl text-amber-600/30 ${animationClass}`}
           style={{
             filter: 'drop-shadow(0 2px 4px rgba(139, 69, 19, 0.1))'
           }}>
        {symbols[type]}
      </div>
    </div>
  );
});
BalineseSeparator.displayName = 'BalineseSeparator';

// Ikony tematyczne dla retreatów
const getRetreatIcon = (id) => {
  const icons = {
    'ubud': '🐒', // małpy
    'ubud-alt': '🌾', // ryż
    'gili-air': '🏝️', // wyspa
    'gili-alt': '🐠', // rafa
    'canggu': '🏄‍♀️', // surf
    'canggu-alt': '🌅', // zachód
    'uluwatu': '🌊' // klify
  };
  
  if (id.includes('ubud')) return Math.random() > 0.5 ? icons.ubud : icons['ubud-alt'];
  if (id.includes('gili')) return Math.random() > 0.5 ? icons['gili-air'] : icons['gili-alt'];
  if (id.includes('canggu')) return Math.random() > 0.5 ? icons.canggu : icons['canggu-alt'];
  if (id.includes('uluwatu')) return icons.uluwatu;
  return '🌺'; // domyślny
};

// Simplified testimonial component - removed as we use TestimonialSlider

const WellnessPage = ({ latestPosts }) => {
  const router = useRouter();
  const posts = useMemo(() => latestPosts || blogPosts.slice(0, 3), [latestPosts]);

  const eventData = useMemo(
    () => ({
      highlights: [
        { id: 'ubud', title: 'Ubud - Serce Duchowe', description: '7 dni programu. Duchowe serce Bali, zanurzenie w kulturze i jodze wśród tarasów ryżowych.', icon: Flower2 },
        { id: 'gili-air', title: 'Gili Air - Rajska Cisza', description: '5 dni relaksu. Rajska wyspa bez samochodów, idealna na relaks i snorkeling.', icon: Sunrise },
        { id: 'uluwatu', title: 'Canggu - Ocean i Joga', description: '10 dni transformacji. Joga na klifach z widokiem na ocean i spektakularne zachody słońca.', icon: Heart },
      ],
    }),
    []
  );

  const testimonials = [
    {
      quote: "Najbardziej transformacyjne doświadczenie mojego życia. Julia stworzyła przestrzeń pełną ciepła i bezpieczeństwa.",
      author: "Anna",
      location: "Warszawa",
      flag: "🇵🇱"
    },
    {
      quote: "Perfekcyjne połączenie jogi, kultury i relaksu. Wracam z Bali jako zupełnie nowa osoba.",
      author: "Marek",
      location: "Kraków",
      flag: "🇵🇱"
    },
    {
      quote: "Grupa była jak druga rodzina. Każdy dzień przynosił nowe odkrycia i głębsze zrozumienie siebie.",
      author: "Kasia",
      location: "Gdańsk",
      flag: "🇵🇱"
    },
    {
      quote: "Bali z Julią to nie tylko retreat, to prawdziwa podróż do siebie. Wróciłam z nową energią i spokojem.",
      author: "Marta",
      location: "Wrocław",
      flag: "🇵🇱"
    }
  ];

  const faqs = [
    {
      question: "Czy retreat jest odpowiedni dla początkujących?",
      answer: "Tak! Nasze retreaty są dostosowane do wszystkich poziomów zaawansowania. Julia prowadzi zajęcia z uwzględnieniem indywidualnych potrzeb każdego uczestnika."
    },
    {
      question: "Co jest wliczone w cenę retreatu?",
      answer: "Cena obejmuje zakwaterowanie, wszystkie posiłki wegetariańskie, codzienne zajęcia jogi i medytacji, wycieczki do lokalnych atrakcji oraz transfer z/do lotniska."
    },
    {
      question: "Jakie są daty najbliższych retreatów?",
      answer: "Najbliższe retreaty odbywają się w czerwcu i wrześniu 2024. Szczegółowe daty znajdziesz w sekcji kalendarz lub skontaktuj się z nami bezpośrednio."
    },
    {
      question: "Czy potrzebuję doświadczenia w jodze?",
      answer: "Nie! Przyjmujemy zarówno początkujących, jak i zaawansowanych praktykujących. Każdy znajdzie coś dla siebie."
    },
    {
      question: "Ile osób uczestniczy w retreatach?",
      answer: "Nasze grupy są małe i kameralne - maksymalnie 12 osób. Dzięki temu każdy uczestnik otrzymuje indywidualną uwagę i wsparcie."
    }
  ];

  const retreats = [
    {
      id: 'ubud-june-2024',
      type: 'Transformacyjny',
      title: 'Ubud - Serce Duchowe',
      startDate: '15 czerwca',
      endDate: '22 czerwca',
      location: 'Ubud, Bali',
      participants: 8,
      maxParticipants: 12,
      price: '2,400 PLN',
      originalPrice: '2,800 PLN',
      description: '7-dniowy retreat w sercu Bali z jogą, medytacją i odkrywaniem lokalnej kultury.',
      available: true,
      status: 'early-bird'
    },
    {
      id: 'gili-air-july-2024',
      type: 'Relaksacyjny',
      title: 'Gili Air - Rajska Cisza',
      startDate: '20 lipca',
      endDate: '25 lipca',
      location: 'Gili Air, Indonezja',
      participants: 6,
      maxParticipants: 10,
      price: '1,800 PLN',
      description: '5-dniowy retreat na rajskiej wyspie bez samochodów. Fokus na relaks i regenerację.',
      available: true,
      status: 'filling-fast'
    },
    {
      id: 'canggu-september-2024',
      type: 'Intensywny',
      title: 'Canggu - Ocean i Joga',
      startDate: '10 września',
      endDate: '20 września',
      location: 'Canggu, Bali',
      participants: 10,
      maxParticipants: 12,
      price: '3,200 PLN',
      description: '10-dniowy intensywny retreat z jogą na plaży i transformacyjnymi praktykami.',
      available: true,
      status: 'confirmed'
    }
  ];

  const combinedItems = useMemo(() => eventData.highlights.map((item) => ({ ...item, type: 'highlight' })), [eventData]);

  const socialLinks = [
    { id: 'instagram', href: 'https://www.instagram.com/fly_with_bakasana', label: 'Instagram', icon: Instagram },
    { id: 'facebook', href: 'https://www.facebook.com/p/Fly-with-bakasana-100077568306563/', label: 'Facebook', icon: Facebook },
    { id: 'fitssey', href: 'https://app.fitssey.com/Flywithbakasana/frontoffice', label: 'Rezerwacje', icon: CalendarCheck },
  ];

  return (
    <WellnessProvider
      config={{
        features: {
          accessibilityEnhancements: true,
          performanceMonitoring: true,
          breathingAnimations: true,
          simpleFadeIn: true,
        }
      }}
    >
      <div className="relative bg-gradient-to-b from-orange-50/30 via-stone-50/20 to-emerald-50/20">
        <HeroSection />

        {/* Sekcja O Retreatach - zgodnie z briefem */}
        <section id="journey-inspiration" className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <OpticalTypography
                variant="h2"
                opticalAlign={true}
                preventOrphans={true}
                className="text-gray-800 tracking-tight mb-6"
              >
                Odkryj Retreaty
              </OpticalTypography>
              <OpticalTypography
                variant="body"
                preventOrphans={true}
                className="text-gray-600 max-w-2xl mx-auto"
              >
                Magiczne miejsca Bali, które odwiedzimy podczas naszego retreatu jogowego
              </OpticalTypography>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {combinedItems.map((item, index) => (
                <Card key={item.id} type={item.type} title={item.title} description={item.description} icon={item.icon} index={index} id={item.id} />
              ))}
            </div>
          </div>
        </section>

        {/* Balijski separator */}
        <BalineseSeparator type="om" />

        {/* Sekcja Zajęcia Online - zgodnie z briefem */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/40">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-12 items-center">
              <div className="lg:col-span-3">
                <OpticalTypography
                  variant="h2"
                  opticalAlign={true}
                  preventOrphans={true}
                  className="text-gray-800 tracking-tight mb-6"
                >
                  Zajęcia Online
                </OpticalTypography>
                <OpticalTypography
                  variant="body"
                  preventOrphans={true}
                  className="text-gray-600 mb-6"
                >
                  Dołącz do regularnych zajęć jogi online. Wszystkie poziomy zaawansowania, harmonogram dostosowany do Twojego rytmu życia.
                </OpticalTypography>
                <OpticalTypography
                  variant="body"
                  preventOrphans={true}
                  className="text-gray-600 mb-6"
                >
                  Poniedziałek - Środa - Piątek: 19:00<br />
                  Sobota: 10:00 (extended session)
                </OpticalTypography>
                <Link
                  href="/zajecia-online"
                  className="inline-flex items-center px-6 py-3 bg-transparent text-gray-800 border border-gray-400 text-sm font-light tracking-wider uppercase transition-all duration-300 hover:bg-gray-100 hover:border-gray-500"
                >
                  Dołącz do zajęć online
                </Link>
              </div>
              <div className="lg:col-span-2">
                <div className="relative aspect-square rounded-full overflow-hidden">
                  <Image
                    src="/images/profile/omnie-opt.webp"
                    alt="Julia w pozycji jogi"
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 40vw"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Sekcja O Julii - BARDZIEJ OSOBISTA */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <div>
                <div className="relative aspect-[3/4] overflow-hidden">
                  <Image
                    src="/images/profile/omnie-opt.webp"
                    alt="Julia Jakubowicz - instruktor jogi"
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                </div>
              </div>
              <div>
                <OpticalTypography
                  variant="h2"
                  opticalAlign={true}
                  preventOrphans={true}
                  className="text-gray-800 tracking-tight mb-6"
                >
                  O Julii
                </OpticalTypography>
                
                {/* Cytat od Julii w stylu kaligrafii */}
                <div className="mb-8 p-6 bg-gradient-to-r from-amber-50/30 to-orange-50/30 border-l-4 border-amber-200/40 relative">
                  <div className="absolute -top-2 -left-2 text-3xl text-amber-600/20">❝</div>
                  <OpticalTypography
                    variant="body"
                    preventOrphans={true}
                    className="text-gray-700 font-light italic text-lg leading-relaxed"
                    style={{
                      fontFamily: 'Georgia, serif',
                      letterSpacing: '0.02em'
                    }}
                  >
                    Bali nauczyło mnie, że prawdziwa praktyka zaczyna się, gdy schodzimy z maty
                  </OpticalTypography>
                  <div className="text-right mt-2">
                    <span className="text-amber-700 text-sm font-light">~ Julia</span>
                  </div>
                </div>
                
                <OpticalTypography
                  variant="body"
                  preventOrphans={true}
                  className="text-gray-600 mb-6"
                >
                  Certyfikowany instruktor jogi z pasją do dzielenia się mądrością starożytnych praktyk. Swoją przygodę z jogą rozpoczęła 8 lat temu, od tamtej pory nie przestaje się rozwijać.
                </OpticalTypography>
                <OpticalTypography
                  variant="body"
                  preventOrphans={true}
                  className="text-gray-600 mb-6"
                >
                  Specjalizuje się w jodze dla początkujących, vinyasa flow i praktykach medytacyjnych. Bali to jej drugie duchowe miejsce na ziemi.
                </OpticalTypography>
                
                {/* Certyfikaty jako eleganckie złote ikony */}
                <div className="flex flex-wrap gap-3 mt-6">
                  {[
                    { name: 'RYT 200', icon: '🏅' },
                    { name: 'Yin Yoga', icon: '🧘‍♀️' },
                    { name: 'Aerial Yoga', icon: '🤸‍♀️' },
                    { name: 'Mindfulness MBSR', icon: '🧠' }
                  ].map((cert) => (
                    <div key={cert.name} className="flex items-center gap-2 bg-amber-50/50 px-3 py-2 rounded-full border border-amber-200/30">
                      <span className="text-sm">{cert.icon}</span>
                      <span className="text-xs text-amber-700 font-light">{cert.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Sekcja Testimoniale - zgodnie z briefem */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/40">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <OpticalTypography
                variant="h2"
                opticalAlign={true}
                preventOrphans={true}
                className="text-gray-800 tracking-tight mb-6"
              >
                Opinie Uczestników
              </OpticalTypography>
            </div>
            
            <TestimonialSlider testimonials={testimonials} />
          </div>
        </section>

        {/* Balijski separator */}
        <BalineseSeparator type="flower" />

        {/* Sekcja Terminy - zgodnie z briefem */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <OpticalTypography
                variant="h2"
                opticalAlign={true}
                preventOrphans={true}
                className="text-gray-800 tracking-tight mb-6"
              >
                Najbliższe Terminy
              </OpticalTypography>
              <OpticalTypography
                variant="body"
                preventOrphans={true}
                className="text-gray-600 max-w-2xl mx-auto"
              >
                Wybierz idealny retreat dla siebie. Wszystkie daty i szczegóły w jednym miejscu.
              </OpticalTypography>
            </div>
            
            <RetreatCalendar retreats={retreats} />
          </div>
        </section>

        {/* Sekcja FAQ - zgodnie z briefem */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/40">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <OpticalTypography
                variant="h2"
                opticalAlign={true}
                preventOrphans={true}
                className="text-gray-800 tracking-tight mb-6"
              >
                Najczęściej Zadawane Pytania
              </OpticalTypography>
            </div>
            
            <FAQAccordion faqs={faqs} />
          </div>
        </section>

        {/* Balijski separator */}
        <BalineseSeparator type="mandala" />

        {/* Sekcja Kontakt - BALIJSKIE ZAMKNIĘCIE */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            {/* Balijskie pozdrowienie */}
            <div className="mb-8">
              <OpticalTypography
                variant="body"
                preventOrphans={true}
                className="text-amber-700 mb-2 italic text-lg"
                style={{
                  fontFamily: 'Georgia, serif',
                  letterSpacing: '0.02em'
                }}
              >
                Om Swastiastu
              </OpticalTypography>
              <OpticalTypography
                variant="caption"
                className="text-gray-500 text-sm font-light"
              >
                Tradycyjne balijskie pozdrowienie
              </OpticalTypography>
            </div>
            
            <OpticalTypography
              variant="h2"
              opticalAlign={true}
              preventOrphans={true}
              className="text-gray-800 tracking-tight mb-6"
            >
              Skontaktuj się z nami
            </OpticalTypography>
            <OpticalTypography
              variant="body"
              preventOrphans={true}
              className="text-gray-600 mb-12"
            >
              Masz pytania? Chcesz dowiedzieć się więcej? Napisz do nas!
            </OpticalTypography>
            
            <div className="flex justify-center space-x-8 mb-12">
              {socialLinks.map((link) => (
                <a
                  key={link.id}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  <SafeIcon Icon={link.icon} className="w-5 h-5" />
                  <span className="text-sm">{link.label}</span>
                </a>
              ))}
            </div>
            
            {/* Mapa z pinami gdzie odbywają się retreaty */}
            <div className="bg-gradient-to-r from-amber-50/30 to-orange-50/30 p-8 rounded-lg border border-amber-200/20">
              <OpticalTypography
                variant="h3"
                className="text-gray-800 mb-4 font-light"
              >
                Nasze Magiczne Miejsca
              </OpticalTypography>
              <div className="flex flex-wrap justify-center gap-6">
                {[
                  { name: 'Ubud', icon: '🐒', coords: 'Serce Duchowe Bali' },
                  { name: 'Gili Air', icon: '🏝️', coords: 'Rajska Wyspa' },
                  { name: 'Canggu', icon: '🏄‍♀️', coords: 'Ocean i Klify' }
                ].map((place) => (
                  <div key={place.name} className="flex items-center gap-2 bg-white/40 px-4 py-2 rounded-full border border-amber-200/30">
                    <span className="text-lg">{place.icon}</span>
                    <div className="text-left">
                      <div className="text-sm font-medium text-gray-800">{place.name}</div>
                      <div className="text-xs text-gray-500">{place.coords}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* WhatsApp floating button - zgodnie z briefem */}
        <div className="fixed bottom-6 right-6 z-50">
          <a
            href="https://wa.me/48123456789"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center w-14 h-14 bg-green-500 text-white rounded-full shadow-lg hover:bg-green-600 transition-colors"
            aria-label="Skontaktuj się przez WhatsApp"
          >
            <MessageSquare size={24} />
          </a>
        </div>
      </div>
    </WellnessProvider>
  );
};

export default WellnessPage;