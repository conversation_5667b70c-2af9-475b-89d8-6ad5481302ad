'use client';

import React from 'react';
import { Calendar, MapPin, Users, Clock } from 'lucide-react';
import OpticalTypography from '@/components/WorldClassDesign/OpticalTypography';

const RetreatCalendar = ({ retreats }) => {
  const handleBooking = (retreatId) => {
    // Redirect to booking form or external booking system
    window.location.href = `/rezerwacja?retreat=${retreatId}`;
  };

  // Funkcja określająca porę roku na Bali
  const getBaliFeason = (startDate) => {
    const month = startDate.includes('czerwca') ? 6 : 
                  startDate.includes('lipca') ? 7 : 
                  startDate.includes('września') ? 9 : 0;
    
    if (month >= 4 && month <= 10) {
      return { season: 'Pora sucha', icon: '☀️', color: 'text-yellow-600' };
    } else {
      return { season: 'Pora deszczowa', icon: '🌧️', color: 'text-blue-600' };
    }
  };

  // Funkcja określająca czy retreat jest bestsellerem
  const isBestseller = (retreat) => {
    return retreat.participants >= retreat.maxParticipants * 0.7;
  };

  return (
    <div className="max-w-6xl mx-auto">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {retreats.map((retreat, index) => (
          <div
            key={retreat.id}
            className={`bg-white/60 backdrop-blur-sm border border-gray-200/50 p-6 transition-all duration-300 hover:shadow-md ${
              retreat.available ? 'hover:border-gray-300' : 'opacity-60'
            }`}
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Calendar className="w-5 h-5 text-gray-400" />
                <OpticalTypography
                  variant="caption"
                  className="text-gray-500 font-light uppercase tracking-wider"
                >
                  {retreat.type}
                </OpticalTypography>
              </div>
              <div className="flex items-center space-x-2">
                {/* Bestseller badge */}
                {isBestseller(retreat) && (
                  <span className="bg-gradient-to-r from-amber-100 to-orange-100 text-amber-700 text-xs font-medium px-2 py-1 rounded-full border border-amber-200">
                    ⭐ Bestseller
                  </span>
                )}
                {retreat.available && (
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse" />
                )}
              </div>
            </div>

            {/* Title */}
            <OpticalTypography
              variant="h3"
              className="text-gray-800 font-light mb-3"
              preventOrphans={true}
            >
              {retreat.title}
            </OpticalTypography>

            {/* Dates */}
            <div className="space-y-2 mb-4">
              <div className="flex items-center text-gray-600">
                <Clock className="w-4 h-4 mr-2" />
                <span className="text-sm">
                  {retreat.startDate} - {retreat.endDate}
                </span>
              </div>
              
              {/* Pora roku na Bali */}
              <div className="flex items-center text-gray-600">
                <span className="text-sm mr-2">{getBaliFeason(retreat.startDate).icon}</span>
                <span className={`text-sm ${getBaliFeason(retreat.startDate).color}`}>
                  {getBaliFeason(retreat.startDate).season}
                </span>
              </div>
              
              <div className="flex items-center text-gray-600">
                <MapPin className="w-4 h-4 mr-2" />
                <span className="text-sm">{retreat.location}</span>
              </div>
              
              <div className="flex items-center text-gray-600">
                <Users className="w-4 h-4 mr-2" />
                <span className="text-sm">
                  {retreat.participants} {retreat.maxParticipants ? `/ ${retreat.maxParticipants}` : ''} osób
                </span>
              </div>
            </div>

            {/* Price */}
            <div className="mb-4">
              <OpticalTypography
                variant="h4"
                className="text-gray-800 font-light"
              >
                {retreat.price}
              </OpticalTypography>
              {retreat.originalPrice && (
                <OpticalTypography
                  variant="caption"
                  className="text-gray-400 line-through ml-2"
                >
                  {retreat.originalPrice}
                </OpticalTypography>
              )}
            </div>

            {/* Description */}
            <OpticalTypography
              variant="body"
              className="text-gray-600 text-sm leading-relaxed mb-6"
              preventOrphans={true}
            >
              {retreat.description}
            </OpticalTypography>

            {/* CTA Button */}
            <button
              onClick={() => handleBooking(retreat.id)}
              disabled={!retreat.available}
              className={`w-full py-3 px-4 text-sm font-light tracking-wider uppercase transition-all duration-300 ${
                retreat.available
                  ? 'bg-transparent text-gray-800 border border-gray-400 hover:bg-gray-100 hover:border-gray-500'
                  : 'bg-gray-100 text-gray-400 border border-gray-200 cursor-not-allowed'
              }`}
            >
              {retreat.available ? 'Zarezerwuj miejsce' : 'Brak miejsc'}
            </button>

            {/* Status badge */}
            {retreat.status && (
              <div className="mt-3 text-center">
                <span className={`inline-block px-3 py-1 text-xs font-light rounded-full ${
                  retreat.status === 'early-bird' 
                    ? 'bg-green-100 text-green-700' 
                    : retreat.status === 'filling-fast'
                    ? 'bg-orange-100 text-orange-700'
                    : 'bg-gray-100 text-gray-700'
                }`}>
                  {retreat.status === 'early-bird' && 'Promocja wczesna'}
                  {retreat.status === 'filling-fast' && 'Szybko się zapełnia'}
                  {retreat.status === 'confirmed' && 'Potwierdzone'}
                </span>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default RetreatCalendar;