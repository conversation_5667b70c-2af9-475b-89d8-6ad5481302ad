@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@300;400&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ==========================================================================
   CSS Variables - BAKASANA Premium Wellness Design
   ========================================================================== */
@layer base {
  :root {
    /* WELLNESS Kolorystyka - Ciepły minimalizm według briefu */
    --color-primary: 44 44 44;         /* <PERSON>iepły szary (#2C2C2C) */
    --color-secondary: 255 255 255;    /* Czysta biel (#FFFFFF) */
    --color-accent: 124 152 133;       /* S<PERSON>łwiowa zieleń (#7C9885) - zachowane */
    --color-background: 255 254 249;   /* Ciepły off-white (#FFFEF9) */
    --color-muted: 245 245 245;        /* Bardzo jasny szary */
    --color-accent-warm: 232 223 211;  /* Piaskowy beż (#E8DFD3) */
    --color-text-light: 102 102 102;   /* Jasny tekst (#666) */

    /* WELLNESS Typografia - Naturalne fonty według briefu */
    --font-sans: 'Source Sans Pro', 'system-ui', '-apple-system', 'BlinkMacSystemFont', sans-serif;
    --font-serif: 'Cormorant Garamond', 'Georgia', 'Times New Roman', serif;
    
    /* Cienie - CIEPŁE BRĄZOWE dla Bali */
    --shadow-soft: 0 1px 3px rgba(0, 0, 0, 0.01);
    --shadow-medium: 0 2px 6px rgba(0, 0, 0, 0.02);
    --shadow-subtle: 0 1px 2px rgba(0, 0, 0, 0.005);
    --shadow-bali: 0 2px 8px rgba(139, 69, 19, 0.08);
    --shadow-bali-hover: 0 4px 16px rgba(139, 69, 19, 0.12);

    
    /* Timing functions */
    --ease-gentle: cubic-bezier(0.23, 1, 0.32, 1);
    --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
    --ease-out-smooth: cubic-bezier(0.16, 1, 0.3, 1);
  }

  * {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* PARALLAX SECTION - DELIKATNY EFEKT */
  .parallax-section {
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  /* FADE IN PRZY SCROLLU */
  .fade-in-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.8s ease-out;
  }

  .fade-in-scroll.visible {
    opacity: 1;
    transform: translateY(0);
  }

  body {
    @apply antialiased;
    font-family: var(--font-sans);
    background: rgb(var(--color-background)); /* Ciepły off-white jako główne tło */
    color: rgb(var(--color-primary));
    letter-spacing: 0.01em;
    line-height: 1.8; /* Zwiększona interlinia zgodnie ze specyfikacją */
    overflow-x: hidden;
    position: relative;
  }

  /* NATURALNA TEKSTURA PAPIERU RYŻOWEGO */
  body::before {
    content: '';
    position: fixed;
    inset: 0;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='ricePaper'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='3' stitchTiles='stitch'/%3E%3CfeColorMatrix values='0 0 0 0 0.96 0 0 0 0 0.94 0 0 0 0 0.90 0 0 0 1 0'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23ricePaper)' opacity='0.6'/%3E%3C/svg%3E");
    opacity: 0.02;
    pointer-events: none;
    z-index: -1;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-serif);
    color: rgb(var(--color-primary));
    font-weight: 200; /* ULTRA-THIN dla H1 elegancji */
    letter-spacing: 0.02em; /* Więcej przestrzeni = więcej elegancji */
    line-height: 1.3;
  }

  /* Logo BAKASANA - 28px */
  .logo {
    font-family: var(--font-serif);
    font-size: 28px;
    font-weight: 400; /* Najlżejsza dostępna waga */
    letter-spacing: -0.01em;
  }

  /* Nagłówki główne H1 - 48-56px, najlżejsza dostępna waga */
  h1 {
    font-size: clamp(3rem, 4.5vw, 3.5rem); /* 48-56px */
    font-weight: 400;
    letter-spacing: -0.02em;
    line-height: 1.2;
  }

  /* Nagłówki sekcji H2 - 36px, waga 400 */
  h2 {
    font-size: clamp(2.25rem, 3.5vw, 2.25rem); /* 36px */
    font-weight: 400;
    letter-spacing: -0.015em;
    line-height: 1.3;
  }

  h3 {
    font-size: clamp(1.5rem, 2.5vw, 2rem);
    font-weight: 400;
    letter-spacing: -0.01em;
    line-height: 1.4;
  }

  h4 {
    font-size: clamp(1.25rem, 2vw, 1.5rem);
    font-weight: 400;
    line-height: 1.5;
  }

  /* Vertical text utility */
  .text-vertical {
    writing-mode: vertical-rl;
    text-orientation: mixed;
  }

  /* Outline text utility */
  .text-outline {
    -webkit-text-stroke: 1px rgb(var(--color-primary));
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  /* Mixed sizes in sentences */
  .text-mixed-large {
    font-size: clamp(1.5rem, 3vw, 2.5rem);
  }

  .text-mixed-small {
    font-size: clamp(0.875rem, 1.5vw, 1rem);
  }

  /* Section numbers in background */
  .section-number {
    position: absolute;
    top: 2rem;
    left: 2rem;
    font-size: clamp(6rem, 10vw, 10rem);
    font-weight: 100;
    color: rgb(var(--color-primary) / 0.03);
    font-family: var(--font-cormorant);
    line-height: 1;
    z-index: 0;
    pointer-events: none;
    transition: color 0.3s ease;
  }

  .section:hover .section-number {
    color: rgb(var(--color-accent) / 0.05);
  }

  /* Thin divider lines */
  .divider-line {
    height: 1px;
    background: linear-gradient(to right, transparent, rgb(var(--color-primary) / 0.1), transparent);
    margin: 4rem 0;
  }

  /* Elegancki separator między sekcjami */
  .section::after {
    content: '';
    display: block;
    width: 80px;
    height: 1px;
    margin: 4rem auto;
    background: linear-gradient(
      to right,
      transparent,
      rgb(var(--color-accent)),
      transparent
    );
  }

  /* Alternatywnie - symbol lotosu */
  .section.lotus-divider::after {
    content: '❀';
    font-size: 2rem;
    color: rgb(var(--color-accent));
    height: auto;
    background: none;
    text-align: center;
    width: auto;
    line-height: 1;
  }

  /* Huge quotes z custom quotation marks */
  .quote-huge {
    font-size: clamp(2rem, 5vw, 4rem);
    font-style: italic;
    font-weight: 300;
    line-height: 1.2;
    color: rgb(var(--color-primary) / 0.8);
    position: relative;
    padding: 2rem 3rem;
  }

  .quote-huge::before {
    content: '"';
    position: absolute;
    top: -1rem;
    left: 0;
    font-size: 6rem;
    color: rgb(var(--color-accent) / 0.3);
    font-family: var(--font-serif);
    line-height: 1;
  }

  /* Pierwsza litera w sekcjach */
  .section-content p:first-of-type::first-letter {
    font-family: var(--font-serif);
    font-size: 4.5rem;
    line-height: 1;
    float: left;
    margin: 0 0.5rem 0 0;
    color: rgb(var(--color-accent));
    font-weight: 300;
  }

  /* Date/location styling */
  .meta-text {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.2em;
    font-weight: 400;
    color: rgb(var(--color-primary) / 0.6);
  }

  /* Eleganckie podtytuły */
  .subtitle {
    @apply text-sm uppercase tracking-widest;
    color: rgb(var(--color-accent) / 0.7);
    letter-spacing: 0.1em;
    font-weight: 400;
  }

  /* Tekst podstawowy - 16-18px, interlinia 2.0, kolor #666 */
  p {
    font-size: clamp(1rem, 1.5vw, 1.125rem); /* 16-18px */
    color: rgb(var(--color-text-light)); /* #666 */
    margin-bottom: 2rem; /* Zwiększone odstępy między paragrafami */
    font-weight: 300; /* Pozostaje OK */
    line-height: 2.0; /* ULTRA-PREMIUM: Line-height 2.0 dla paragrafów */
  }

  p:last-child {
    margin-bottom: 0;
  }

  a {
    color: rgb(var(--color-accent));
    text-decoration: none;
    position: relative;
    font-weight: 300;
    transition: opacity 0.3s ease;
  }

  a:hover {
    opacity: 0.7;
  }

  ::selection {
    color: rgb(var(--color-primary));
    background-color: rgb(var(--color-muted) / 0.3);
  }

  img {
    @apply block max-w-full h-auto;
    image-rendering: -webkit-optimize-contrast;
  }

  /* Image lazy loading z fade */
  .lazy-image {
    opacity: 0;
    transition: opacity 1s ease-in-out;
  }

  .lazy-image.loaded {
    opacity: 1;
  }

  /* Placeholder blur */
  .image-container {
    background: linear-gradient(
      45deg,
      #f5f2ed 25%,
      #e8e5e0 50%,
      #f5f2ed 75%
    );
    background-size: 400% 400%;
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
  }

  /* Balijskie animacje */
  @keyframes breathe {
    0%, 100% { 
      transform: scale(1);
      opacity: 0.3;
    }
    50% { 
      transform: scale(1.05);
      opacity: 0.5;
    }
  }

  @keyframes gentleWave {
    0%, 100% { 
      transform: translateX(0) rotate(0deg);
      opacity: 0.1;
    }
    50% { 
      transform: translateX(10px) rotate(2deg);
      opacity: 0.2;
    }
  }

  .breathing-om {
    animation: breathe 3s ease-in-out infinite;
  }

  .breathing-lotus {
    animation: breathe 4s ease-in-out infinite;
  }

  .gentle-wave {
    animation: gentleWave 6s ease-in-out infinite;
  }

  /* Balijskie kursory */
  .cursor-lotus {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" font-size="14">🪷</text></svg>'), pointer;
  }

  .cursor-om {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" font-size="12">ॐ</text></svg>'), pointer;
  }

  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply rounded-full;
    background-color: rgb(var(--color-accent) / 0.20);
    transition: background-color 0.3s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgb(var(--color-accent) / 0.40);
  }
}

@layer components {
  /* USUNIĘTE: GLASSMORPHISM - może spowalniać, nie jest niezbędny dla premium efektu */
  /* Filozofia: Prostota > Efekty */

  /* HERO OVERLAY EFFECTS - Efekty nakładek */
  .hero-overlay-gradient {
    background: linear-gradient(180deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.2) 100%);
  }

  .hero-overlay-subtle {
    background: rgba(0, 0, 0, 0.4);
  }

  .hero-overlay-strong {
    background: rgba(0, 0, 0, 0.5);
  }

  /* BALI AUTHENTIC GRADIENT - Prawdziwy balijskistyl */
  .hero-overlay-bali {
    background: linear-gradient(180deg, rgba(0,0,0,0.3) 0%, transparent 30%);
    pointer-events: none;
  }

  /* LOTUS ANIMATION - Subtelna animacja lotosu */
  .lotus-pulse {
    animation: lotus-breathe 4s ease-in-out infinite;
    display: inline-block;
    transform-origin: center;
  }

  @keyframes lotus-breathe {
    0%, 100% {
      transform: scale(1) rotate(0deg);
      opacity: 0.7;
    }
    50% {
      transform: scale(1.1) rotate(2deg);
      opacity: 1;
    }
  }

  /* IMAGE FILTERS - Filtry obrazów */
  .image-soft {
    filter: contrast(0.9) brightness(1.1);
    opacity: 0.85;
  }
  
  .image-muted {
    filter: contrast(0.8) brightness(1.2);
    opacity: 0.75;
  }

  /* ULTRA MINIMAL HERO - Minimalistyczny Hero */
  .hero-minimal {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
  }
  
  .hero-minimal-text {
    font-weight: 200;
    letter-spacing: 0.05em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .hero-minimal-subtitle {
    font-weight: 300;
    opacity: 0.8;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .hero-minimal-button {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.8);
    border-radius: 0;
    color: white;
    font-weight: 300;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }
  
  .hero-minimal-button:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 1);
  }
  
  .hero-scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    opacity: 0.6;
    cursor: pointer;
    transition: opacity 0.3s ease;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
  
  .hero-scroll-indicator:hover {
    opacity: 1;
  }

  /* ULTRA-PREMIUM LAYOUT - WIĘCEJ BIAŁYCH PRZESTRZENI */
  .section-padding {
    @apply py-32; /* 128px na mniejszych ekranach */
  }

  /* Profesjonalne marginesy - 160px na większych ekranach */
  @media (min-width: 768px) {
    .section-padding {
      @apply py-160; /* 160px na większych ekranach (nie 120px) */
    }
  }

  .container-unified {
    @apply max-w-content mx-auto px-8; /* max-w-content = 1200px (węższy = bardziej ekskluzywny) */
  }

  .section-unified-title {
    @apply text-center mb-32; /* mb-32 (nie mb-20) - między elementami */
  }

  .section-unified-title h2 {
    @apply mb-6;
  }

  .section-unified-title p {
    @apply text-xl font-light;
  }

  /* SIATKA 12-KOLUMNOWA */
  .grid-12-col {
    @apply grid grid-cols-12 gap-6;
  }

  @media (min-width: 768px) {
    .grid-12-col {
      @apply gap-8; /* 32px guttery na większych ekranach */
    }
  }

  /* Treść główna - 8 kolumn */
  .main-content {
    @apply col-span-12 md:col-span-8;
  }

  /* Sidebary - 4 kolumny */
  .sidebar {
    @apply col-span-12 md:col-span-4;
  }

  /* BIAŁE PRZESTRZENIE - DUŻO POWIETRZA */
  .text-breathing {
    @apply p-10 md:p-16; /* Dużo przestrzeni wokół tekstu */
  }

  /* PROPORCJE ZŁOTEGO PODZIAŁU */
  .golden-ratio-section {
    aspect-ratio: 1.618; /* Złoty podział */
  }

  /* Elementy w proporcjach 1:1.618 */
  .golden-ratio-container {
    display: grid;
    grid-template-columns: 1fr 1.618fr; /* Proporcje złotego podziału */
    gap: 2rem;
  }

  @media (max-width: 768px) {
    .golden-ratio-container {
      grid-template-columns: 1fr;
    }
  }
  /* ULTRA MINIMALISTYCZNE PRZYCISKI - PROSTOKĄTNE, BEZ ZAOKRĄGLEŃ */
  .btn-primary {
    @apply px-16 py-5 text-sm uppercase tracking-wider;
    background: transparent;
    border: 1px solid rgb(var(--color-primary) / 0.3);
    color: rgb(var(--color-primary));
    transition: all 0.3s ease;
    font-weight: 300;
    border-radius: 0; /* Prostokątne - zero zaokrągleń */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Ostre cienie */
  }

  .btn-primary:hover {
    background: rgb(var(--color-primary));
    color: rgb(var(--color-secondary));
    border-color: rgb(var(--color-primary));
    /* Tylko zmiana koloru, bez transformacji */
  }

  .btn-secondary {
    @apply px-16 py-5 text-sm uppercase tracking-wider;
    background: transparent;
    border: 1px solid rgb(var(--color-accent) / 0.3);
    color: rgb(var(--color-accent));
    transition: all 0.3s ease;
    font-weight: 300;
    border-radius: 0; /* Prostokątne - zero zaokrągleń */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Ostre cienie */
  }

  .btn-secondary:hover {
    background: rgb(var(--color-accent));
    color: rgb(var(--color-secondary));
    border-color: rgb(var(--color-accent));
    /* Tylko zmiana koloru, bez transformacji */
  }

  /* ULTRA MINIMALISTYCZNE KARTY - PROSTOKĄTNE Z ELEGANCKIMI KRAWĘDZIAMI */
  .card {
    background: transparent; /* Zlane z tłem */
    border-radius: 0; /* Prostokątne */
    border: 1px solid rgba(0, 0, 0, 0.05); /* Eleganckie krawędzie */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Ostre cienie */
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .card:hover {
    border-color: rgba(0, 0, 0, 0.08);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    /* Tylko zmiana koloru, bez transformacji */
  }

  .card-content {
    @apply p-10; /* Zwiększone padding dla większej przestrzeni */
  }

  .card-image {
    @apply relative overflow-hidden;
    aspect-ratio: 16/9;
  }

  .card-image img {
    @apply w-full h-full object-cover;
  }

  /* Ultra minimalistyczna nawigacja */
  .navbar {
    @apply fixed top-0 w-full;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    z-index: 50;
    transition: all 0.3s ease;
  }

  .nav-content {
    @apply flex justify-between items-center py-6;
  }

  .nav-link {
    @apply relative px-6 py-3;
    color: rgb(var(--color-primary) / 0.7);
    font-weight: 300;
    transition: opacity 0.3s ease;
  }

  .nav-link:hover {
    opacity: 0.7;
  }

  .nav-link[aria-current="page"] {
    color: rgb(var(--color-accent));
  }

  .hero-section {
    @apply relative w-full min-h-screen flex items-center justify-center overflow-hidden;
    background: rgb(var(--color-secondary));
    background-attachment: fixed;
  }

  /* Delikatne cząsteczki kurzu/światła */
  .hero-section::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 30%, rgba(212, 165, 116, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(139, 115, 85, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(212, 165, 116, 0.06) 0%, transparent 50%);
    opacity: 0.6;
    animation: float 20s linear infinite;
    pointer-events: none;
  }

  @keyframes float {
    0% { transform: translateY(0) translateX(0) rotate(0deg); }
    33% { transform: translateY(-30px) translateX(20px) rotate(120deg); }
    66% { transform: translateY(-60px) translateX(-10px) rotate(240deg); }
    100% { transform: translateY(-100px) translateX(30px) rotate(360deg); }
  }

  .hero-content {
    @apply text-center max-w-4xl mx-auto px-6 relative z-10;
  }

  .hero-title {
    @apply mb-8 animate-fade-in;
    animation-delay: 0.2s;
    font-weight: 300;
  }

  .hero-subtitle {
    @apply text-lg md:text-xl mb-12 animate-fade-in;
    color: rgb(var(--color-primary) / 0.85);
    animation-delay: 0.4s;
    font-weight: 300;
  }

  .hero-actions {
    @apply flex flex-col sm:flex-row gap-4 justify-center animate-fade-in;
    animation-delay: 0.6s;
  }

  .section {
    @apply relative py-24 lg:py-32;
  }

  .section-title {
    @apply text-center mb-16;
  }

  .section-title h2 {
    @apply mb-6;
    color: rgb(var(--color-primary));
  }

  .section-title p {
    @apply max-w-3xl mx-auto text-lg;
    color: rgb(var(--color-primary) / 0.7);
  }

  .content-spacing {
    @apply space-y-12 lg:space-y-16;
  }

  .decorative-line {
    @apply w-24 h-px mx-auto mb-8;
    background: linear-gradient(to right, transparent, rgb(var(--color-accent) / 0.30), transparent);
  }

  .glass-effect {
    @apply border rounded-lg;
    background-color: rgba(248, 246, 243, 0.95);
    border-color: rgba(232, 229, 224, 0.5);
  }

  .gradient-text {
    background: linear-gradient(to right, rgb(var(--color-accent)), rgb(var(--color-primary)));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .image-hover-zoom {
    @apply overflow-hidden relative rounded-lg;
  }
  
  .image-hover-zoom img {
    @apply transition-transform duration-700;
  }
  
  .group:hover .image-hover-zoom img {
    @apply scale-105;
  }

  .grid-auto {
    @apply grid gap-8;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .grid-featured {
    @apply grid gap-8 lg:grid-cols-2;
  }

  .grid-gallery {
    @apply grid gap-6;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .form-group {
    @apply mb-6;
  }

  .form-label {
    @apply block font-medium mb-2;
    color: rgb(var(--color-primary));
  }

  .form-input {
    @apply w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 transition-all;
    background: rgb(var(--color-secondary));
    border-color: rgb(var(--color-accent) / 0.2);
    color: rgb(var(--color-primary));
  }

  .form-input:focus {
    border-color: rgb(var(--color-accent));
    box-shadow: 0 0 0 3px rgb(var(--color-accent) / 0.1);
  }

  .form-textarea {
    @apply form-input resize-none;
    min-height: 120px;
  }

  .quote {
    @apply relative p-8 glass-effect;
  }

  .quote::before {
    content: '"';
    @apply absolute -top-4 left-8 text-6xl font-serif;
    color: rgb(var(--color-accent) / 0.25);
  }

  .quote-text {
    @apply text-lg italic mb-4;
    color: rgb(var(--color-primary) / 0.8);
  }

  .quote-author {
    @apply font-medium;
    color: rgb(var(--color-primary));
  }

  /* Enhanced section styling */
  .enhanced-section {
    @apply relative py-8;
  }

  /* Gradient text effects */
  .gradient-text-golden {
    background: linear-gradient(to right, rgb(var(--color-accent)), rgb(var(--color-primary)));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .gradient-text-accent {
    background: linear-gradient(to right, rgb(var(--color-accent)), rgb(var(--color-primary)));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Enhanced decorative elements */
  .decorative-dot {
    @apply w-2 h-2 rounded-full;
    background: linear-gradient(to right, rgb(var(--color-accent)), rgb(var(--color-primary)));
    animation: pulse 2s infinite;
  }

  .decorative-line-enhanced {
    @apply h-px;
    background: linear-gradient(to right, transparent, rgb(var(--color-accent) / 0.3), transparent);
  }

  /* Floating animation for badges */
  .floating-badge {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
  }

  /* Enhanced hover effects */
  .hover-lift {
    transition: all 0.4s var(--ease-gentle);
  }

  .hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  /* Subtle glow effects */
  .glow-accent {
    box-shadow: 0 0 20px rgb(var(--color-accent) / 0.1);
  }




}

@layer utilities {
  .section-padding {
    @apply py-24 lg:py-32;
  }

  .container-padding {
    @apply px-6 sm:px-8 lg:px-12;
  }

  .max-width-content {
    @apply max-w-7xl mx-auto;
  }

  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  .shadow-soft {
    box-shadow: var(--shadow-bali);
  }

  .shadow-medium {
    box-shadow: var(--shadow-bali-hover);
  }

  .shadow-subtle {
    box-shadow: var(--shadow-subtle);
  }

  /* Profesjonalna galeria - jednolita siatka */
  .gallery {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2px;
  }

  .gallery img {
    aspect-ratio: 1/1;
    object-fit: cover;
    transition: transform 0.6s cubic-bezier(0.22, 1, 0.36, 1);
    cursor: pointer;
  }

  .gallery img:hover {
    transform: scale(1.02);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(15px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* ANIMACJA SCROLL INDICATOR */
  @keyframes scroll-down {
    0%, 100% {
      transform: translateY(0);
      opacity: 0.4;
    }
    50% {
      transform: translateY(8px);
      opacity: 1;
    }
  }

  .animate-scroll-down {
    animation: scroll-down 2s ease-in-out infinite;
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes floatGentle {
    0%, 100% { 
      transform: translateY(0px); 
    }
    50% { 
      transform: translateY(-4px); 
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes hoverLift {
    to {
      transform: translateY(-2px);
    }
  }

  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .animate-fade-in {
    animation: fadeIn 1.2s ease forwards;
  }

  .animate-slide-up {
    animation: slideUp 1s ease forwards;
  }

  .animate-float {
    animation: floatGentle 6s ease-in-out infinite;
  }

  .animate-scale-in {
    animation: scaleIn 0.8s ease forwards;
  }

  .animate-hover-lift {
    animation: hoverLift 0.3s ease forwards;
  }

  .delay-100 { animation-delay: 100ms; }
  .delay-200 { animation-delay: 200ms; }
  .delay-300 { animation-delay: 300ms; }
  .delay-400 { animation-delay: 400ms; }
  .delay-500 { animation-delay: 500ms; }

  .aspect-square {
    aspect-ratio: 1/1;
  }

  .aspect-video {
    aspect-ratio: 16/9;
  }

  .aspect-photo {
    aspect-ratio: 4/3;
  }

  .aspect-portrait {
    aspect-ratio: 3/4;
  }

  .image-container {
    @apply relative overflow-hidden rounded-lg;
  }

  .image-container img {
    @apply w-full h-full object-cover transition-transform duration-700;
  }

  .image-container:hover img {
    @apply scale-[1.02];
  }

  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    --tw-ring-color: rgb(var(--color-accent) / 0.4);
  }

  .interactive {
    @apply transition-all cursor-pointer;
    transition-duration: 0.3s;
  }

  .interactive:hover {
    transform: translateY(-1px);
  }

  .interactive:active {
    transform: translateY(0);
  }

  .blur-soft {
    backdrop-filter: blur(8px);
  }

  .blur-medium {
    backdrop-filter: blur(12px);
  }

  .blur-strong {
    backdrop-filter: blur(16px);
  }

  .transition-gentle {
    transition: all 0.4s ease;
  }

  .transition-smooth {
    transition: all 0.3s ease;
  }

  .transition-soft {
    transition: all 0.2s ease;
  }

  /* Tło sekcji - jednolite kolory */
  .section-bg {
    background: rgb(var(--color-secondary));
  }

  /* Delikatne animacje */
  .animate-gentle {
    animation: fadeInGentle 1.2s ease forwards;
    opacity: 0;
  }

  @keyframes fadeInGentle {
    from {
      opacity: 0;
      transform: translateY(8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Opóźnienia dla animacji */
  
  /* ELEGANCKIE KRAWĘDZIE - UNIWERSALNE KLASY */
  .elegant-border {
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }
  
  .elegant-border-hover:hover {
    border-color: rgba(0, 0, 0, 0.08);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  }
  
  /* PROSTOKĄTNE WSZYSTKO */
  .rectangular {
    border-radius: 0 !important;
  }
  
  .rectangular-subtle {
    border-radius: 2px !important; /* Minimalne zaokrąglenia */
  }
  
  /* PROFESJONALNE ODSTĘPY */
  .professional-spacing {
    padding: 5rem 0; /* 80px */
  }
  
  @media (min-width: 768px) {
    .professional-spacing {
      padding: 7.5rem 0; /* 120px */
    }
  }
  
  /* SIATKA ZAWARTOŚCI */
  .content-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1.5rem;
  }
  
  @media (min-width: 768px) {
    .content-grid {
      gap: 2rem;
    }
  }
  
  .content-main {
    grid-column: span 12;
  }
  
  @media (min-width: 768px) {
    .content-main {
      grid-column: span 8;
    }
  }
  
  .content-sidebar {
    grid-column: span 12;
  }
  
  @media (min-width: 768px) {
    .content-sidebar {
      grid-column: span 4;
    }
  }
  .delay-50 { animation-delay: 50ms; }
  .delay-150 { animation-delay: 150ms; }
  .delay-250 { animation-delay: 250ms; }
  .delay-350 { animation-delay: 350ms; }
  .delay-450 { animation-delay: 450ms; }



  /* Dodatkowe klasy dla lepszej spójności */
  .text-light {
    font-weight: 300;
  }

  .heading-light {
    font-weight: 300;
    letter-spacing: -0.01em;
  }

  /* Elegancki loader - mindful breathing */
  .mindful-loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  .breath-circle {
    width: 60px;
    height: 60px;
    border: 2px solid rgb(var(--color-accent));
    border-radius: 50%;
    animation: breathe 4s ease-in-out infinite;
  }

  .breath-text {
    margin-top: 1rem;
    font-size: 0.875rem;
    color: rgb(var(--color-accent));
    font-weight: 300;
    letter-spacing: 0.1em;
  }

  @keyframes breathe {
    0%, 100% {
      transform: scale(1);
      opacity: 0.3;
    }
    50% {
      transform: scale(1.2);
      opacity: 1;
    }
  }

  /* Fallback loader */
  .loader {
    width: 40px;
    height: 40px;
    border: 1px solid rgb(var(--color-soft-sage));
    border-top-color: rgb(var(--color-accent));
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  /* Mikro-animacje dla liczb/statystyk */
  .stat-number {
    font-variant-numeric: tabular-nums;
    letter-spacing: -0.02em;
    font-weight: 300;
  }

  @keyframes countUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-count-up {
    animation: countUp 0.8s ease forwards;
  }

  /* Eleganckie powiadomienia/toasty */
  .toast {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: rgb(var(--color-primary));
    color: rgb(var(--color-secondary));
    padding: 1rem 2rem;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
    font-weight: 300;
    border-radius: 0;
    box-shadow: var(--shadow-medium);
    animation: slideInToast 0.3s ease;
    z-index: 1000;
  }

  @keyframes slideInToast {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .toast.success {
    background: rgb(var(--color-accent));
    color: rgb(var(--color-secondary));
  }

  .toast.error {
    background: #dc2626;
    color: white;
  }

  /* Opcjonalne wsparcie dla dark mode */
  @media (prefers-color-scheme: dark) {
    :root {
      --color-primary: 250 250 248;     /* Jasny tekst w dark mode */
      --color-secondary: 44 44 44;      /* Ciemne tło w dark mode */
      --color-accent: 168 196 176;      /* Jaśniejszy szałwiowy w dark mode */
      --color-background: 26 26 26;     /* Bardzo ciemne tło */
      --color-muted: 60 60 60;          /* Ciemny szary */
    }
  }

  /* Smooth transitions dla zmiany motywu */
  * {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  }

  .section-unified-title {
    text-align: center;
    margin-bottom: 4rem; /* mb-16 */
  }

  .section-unified-title h2 {
    margin-bottom: 1.5rem; /* mb-6 */
    font-weight: 300; /* font-light */
    color: rgb(var(--color-accent));
  }

  .section-unified-title p {
    max-width: 48rem; /* max-w-3xl */
    margin-left: auto;
    margin-right: auto;
    font-size: 1.125rem; /* text-lg */
    font-weight: 300; /* font-light */
    color: rgb(var(--color-primary) / 0.85);
  }

  /* Spójne style dla wszystkich kontenerów */
  .container-unified {
    max-width: 80rem; /* max-w-7xl */
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 640px) {
    .container-unified {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-unified {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  .container-unified-content {
    max-width: 64rem; /* max-w-5xl */
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 640px) {
    .container-unified-content {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-unified-content {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  /* Spójne style dla wszystkich grid-ów */
  .grid-unified {
    display: grid;
    gap: 2rem; /* gap-8 */
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .grid-unified-small {
    display: grid;
    gap: 1.5rem; /* gap-6 */
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .grid-unified-large {
    display: grid;
    gap: 2.5rem; /* gap-10 */
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }

  /* BAKASANA - Dodatkowe klasy utility */
  .section-padding {
    padding-top: 7.5rem; /* 120px */
    padding-bottom: 7.5rem; /* 120px */
  }

  @media (max-width: 768px) {
    .section-padding {
      padding-top: 5rem; /* 80px na mobile */
      padding-bottom: 5rem; /* 80px na mobile */
    }
  }

  /* Gradient radial dla efektów */
  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }

  /* BAKASANA - Hero Section */
  .hero-section {
    position: relative;
    width: 100%;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: rgb(var(--color-secondary));
  }

  .hero-content {
    position: relative;
    z-index: 10;
    text-align: center;
    max-width: 64rem;
    margin: 0 auto;
    padding: 0 1.5rem;
  }

  .hero-title {
    font-size: clamp(3.5rem, 8vw, 5rem);
    font-family: var(--font-serif);
    font-weight: 400;
    margin-bottom: 2rem;
    letter-spacing: -0.02em;
    color: rgb(var(--color-primary));
    line-height: 1.1;
  }

  .hero-subtitle {
    font-size: clamp(1.125rem, 2.5vw, 1.25rem);
    margin-bottom: 3rem;
    color: rgb(var(--color-primary) / 0.85);
    font-weight: 300;
    max-width: 36rem;
    margin-left: auto;
    margin-right: auto;
  }

  .hero-actions {
    animation: fadeIn 1.2s ease forwards;
    animation-delay: 0.6s;
    opacity: 0;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* BAKASANA - Dodatkowe klasy utility */
  .shadow-soft {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  }

  .shadow-medium {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  }

  .rounded-xl {
    border-radius: 0.75rem;
  }

  .aspect-4\/3 {
    aspect-ratio: 4/3;
  }

  .aspect-4\/5 {
    aspect-ratio: 4/5;
  }

  .space-y-8 > * + * {
    margin-top: 2rem;
  }

  .space-y-6 > * + * {
    margin-top: 1.5rem;
  }

  .leading-relaxed {
    line-height: 1.625;
  }

  .tracking-wider {
    letter-spacing: 0.05em;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .font-light {
    font-weight: 300;
  }

  .text-text-light {
    color: rgb(var(--color-text-light));
  }

  .text-accent {
    color: rgb(var(--color-accent));
  }

  .text-primary {
    color: rgb(var(--color-primary));
  }

  .text-secondary {
    color: rgb(var(--color-secondary));
  }

  .bg-secondary {
    background-color: rgb(var(--color-secondary));
  }

  .bg-background {
    background-color: rgb(var(--color-background));
  }

  .bg-accent {
    background-color: rgb(var(--color-accent));
  }

  .bg-accent-dark {
    background-color: rgb(var(--color-accent-dark));
  }

  .hover\:bg-accent-dark:hover {
    background-color: rgb(var(--color-accent-dark));
  }

  .hover\:bg-primary:hover {
    background-color: rgb(var(--color-primary));
  }

  .hover\:text-secondary:hover {
    color: rgb(var(--color-secondary));
  }

  .border-primary {
    border-color: rgb(var(--color-primary));
  }

  .border-accent {
    border-color: rgb(var(--color-accent));
  }

  .rounded-full {
    border-radius: 9999px;
  }

  .transition-all {
    transition: all 0.3s ease;
  }

  .duration-300 {
    transition-duration: 300ms;
  }

  .duration-500 {
    transition-duration: 500ms;
  }

  .duration-700 {
    transition-duration: 700ms;
  }

  .hover\:scale-105:hover {
    transform: scale(1.05);
  }

  .hover\:-translate-y-2:hover {
    transform: translateY(-0.5rem);
  }

  .group:hover .group-hover\:scale-105 {
    transform: scale(1.05);
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 85vh;
    padding-left: 2rem;
    padding-right: 2rem;
  }

  /* Więcej przestrzeni na mobile - nie zmniejszaj paddingów */
  .section-padding {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .card-content {
    padding: 2rem;
  }

  .btn {
    @apply px-6 py-3 text-sm;
  }

  .nav-link {
    @apply px-4 py-2;
  }

  .hero-title {
    @apply text-3xl md:text-4xl;
  }

  .hero-subtitle {
    @apply text-lg md:text-xl;
  }

  .animate-fade-in,
  .animate-slide-up,
  .animate-scale-in {
    animation-duration: 0.8s;
  }

  /* Ukryj mniej ważne elementy na mobile */
  .mobile-hidden {
    display: none;
  }

  /* Kreatywny stack - niektóre elementy obok siebie */
  .mobile-creative-stack {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  /* Section numbers mniejsze na mobile */
  .section-number {
    font-size: clamp(3rem, 8vw, 6rem);
    top: 1rem;
    left: 1rem;
  }

  /* Quote huge mniejszy na mobile */
  .quote-huge {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
  }

  .quote-huge::before {
    font-size: clamp(2rem, 6vw, 4rem);
    top: -1rem;
    left: -1rem;
  }

  /* Gesture hints */
  .gesture-hint {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: rgba(255, 255, 255, 0.9);
    padding: 0.5rem;
    border-radius: 50%;
    animation: bounce 2s infinite;
    z-index: 40;
  }

  /* Swipe hint dla mobile */
  .swipe-hint {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    animation: swipeUp 2s ease-in-out infinite;
    color: rgb(var(--color-accent) / 0.7);
    font-size: 0.875rem;
    font-weight: 300;
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }

  @keyframes swipeUp {
    0%, 100% {
      transform: translateX(-50%) translateY(0);
      opacity: 0.7;
    }
    50% {
      transform: translateX(-50%) translateY(-10px);
      opacity: 1;
    }
  }
}

@media (max-width: 480px) {
  .hero-section {
    min-height: 80vh;
  }

  .section-padding {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .card-content {
    padding: 1rem;
  }

  .btn {
    @apply px-4 py-2 text-xs;
  }
}

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-fade-in,
  .animate-slide-up,
  .animate-scale-in,
  .animate-hover-lift,
  .animate-float {
    animation: none !important;
  }
}

@media print {
  * {
    box-shadow: none !important;
    text-shadow: none !important;
    background: transparent !important;
    color: black !important;
  }
  
  .navbar,
  .btn,
  .hero-section,
  footer {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
    font-size: 12pt;
    line-height: 1.4;
  }

  a,
  a:visited {
    text-decoration: underline;
    color: #444 !important;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  img {
    max-width: 100% !important;
    page-break-inside: avoid;
  }

  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }

  h2, h3 {
    page-break-after: avoid;
  }

  .card {
    border: 1px solid #ddd !important;
    page-break-inside: avoid;
  }
}

/* React Big Calendar Styles - loaded dynamically in component */

.custom-calendar {
  font-family: var(--font-inter);
}

.custom-calendar .rbc-header {
  background-color: rgb(var(--color-primary) / 0.05);
  color: rgb(var(--color-primary));
  font-weight: 500;
  padding: 12px 8px;
  border-bottom: 1px solid rgb(var(--color-primary) / 0.1);
}

.custom-calendar .rbc-today {
  background-color: rgb(var(--color-primary) / 0.05);
}

.custom-calendar .rbc-off-range-bg {
  background-color: rgb(var(--color-primary) / 0.02);
}

.custom-calendar .rbc-event {
  border-radius: 6px;
  border: none;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
}

.custom-calendar .rbc-event:focus {
  outline: 2px solid rgb(var(--color-primary) / 0.5);
}

.custom-calendar .rbc-toolbar {
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 12px;
}

.custom-calendar .rbc-toolbar button {
  background-color: white;
  border: 1px solid rgb(var(--color-primary) / 0.2);
  color: rgb(var(--color-primary));
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.custom-calendar .rbc-toolbar button:hover {
  background-color: rgb(var(--color-primary) / 0.05);
  border-color: rgb(var(--color-primary) / 0.3);
}

.custom-calendar .rbc-toolbar button.rbc-active {
  background-color: rgb(var(--color-primary));
  color: white;
  border-color: rgb(var(--color-primary));
}

.custom-calendar .rbc-month-view {
  border: 1px solid rgb(var(--color-primary) / 0.1);
  border-radius: 12px;
  overflow: hidden;
}

.custom-calendar .rbc-date-cell {
  padding: 8px;
  text-align: center;
}

.custom-calendar .rbc-date-cell > a {
  color: rgb(var(--color-primary));
  font-weight: 500;
}

@media (max-width: 768px) {
  .custom-calendar .rbc-toolbar {
    flex-direction: column;
    align-items: center;
  }

  .custom-calendar .rbc-toolbar-label {
    order: -1;
    margin-bottom: 12px;
    font-size: 18px;
    font-weight: 600;
  }
}

/* ==================================================
   TOP 1% DESIGN SYSTEM - WORLD CLASS QUALITY
   Inspired by: Apple, Linear.app, Stripe, Kinfolk
   ================================================== */

/* Premium Typography */
.optical-typography {
  text-rendering: optimizeLegibility;
  font-feature-settings: "liga" 1, "kern" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Invisible Luxury - Subtle textures */
.noise-texture {
  position: relative;
}

.noise-texture::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='1' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  background-size: 256px 256px;
  opacity: 0.015;
  pointer-events: none;
  z-index: 1;
}

/* Premium Animations */
@keyframes magnetic-hover {
  0% { transform: translate(0, 0); }
  100% { transform: translate(var(--mouse-x), var(--mouse-y)); }
}

@keyframes breathing {
  0%, 100% { 
    transform: scale(1); 
    opacity: 0.9; 
  }
  50% { 
    transform: scale(1.02); 
    opacity: 1; 
  }
}

@keyframes floating {
  0%, 100% { 
    transform: translateY(0px); 
  }
  50% { 
    transform: translateY(-10px); 
  }
}

@keyframes shimmer {
  0% { 
    background-position: -200% 0; 
  }
  100% { 
    background-position: 200% 0; 
  }
}

@keyframes pulse-glow {
  0%, 100% { 
    opacity: 0.7; 
    transform: scale(1); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.05); 
  }
}

/* Premium Micro-interactions */
.magnetic-element {
  transition: transform 0.2s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: transform;
}

.breathing-element {
  animation: breathing 4s ease-in-out infinite;
  will-change: transform;
}

.floating-element {
  animation: floating 6s ease-in-out infinite;
  will-change: transform;
}

.shimmer-element {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Premium Scroll Behaviors */
.smooth-scroll {
  scroll-behavior: smooth;
}

.scroll-snap-container {
  scroll-snap-type: y mandatory;
}

.scroll-snap-item {
  scroll-snap-align: start;
}

/* Premium Glass Effects */
.glass-effect {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-effect-dark {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Premium Shadows */
.shadow-premium {
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24);
}

.shadow-premium-hover {
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.12);
}

.shadow-premium-active {
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.12),
    0 1px 3px rgba(0, 0, 0, 0.08);
}

/* Premium Gradients */
.gradient-premium {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-premium-soft {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.gradient-premium-warm {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

/* Premium Borders */
.border-premium {
  border: 1px solid;
  border-image: linear-gradient(45deg, transparent, rgba(124, 152, 133, 0.1), transparent) 1;
}

.border-premium-glow {
  border: 1px solid rgba(124, 152, 133, 0.2);
  box-shadow: 0 0 0 1px rgba(124, 152, 133, 0.1);
}

/* Premium Focus States */
.focus-premium:focus {
  outline: none;
  box-shadow: 
    0 0 0 2px rgba(124, 152, 133, 0.2),
    0 0 0 4px rgba(124, 152, 133, 0.1);
}

.focus-premium:focus-visible {
  outline: 2px solid #7C9885;
  outline-offset: 2px;
}

/* Premium Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.loading-pulse {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Premium Responsive Design */
@media (max-width: 768px) {
  .magnetic-element {
    transform: none !important;
    transition: none !important;
  }
  
  .breathing-element,
  .floating-element {
    animation-duration: 6s !important;
  }
}

/* Premium Accessibility */
@media (prefers-reduced-motion: reduce) {
  .magnetic-element,
  .breathing-element,
  .floating-element,
  .shimmer-element,
  .loading-skeleton,
  .loading-pulse {
    animation: none !important;
    transition: none !important;
  }
  
  .smooth-scroll {
    scroll-behavior: auto !important;
  }
}

@media (prefers-color-scheme: dark) {
  .glass-effect {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .loading-skeleton {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }
}

/* Premium Performance Optimizations */
.premium-performance {
  contain: layout style paint;
  will-change: transform;
}

.premium-performance-hover:hover {
  contain: layout style paint;
  will-change: transform;
}

/* Premium Color Palettes */
:root {
  --color-premium-primary: #7C9885;
  --color-premium-secondary: #A8C4B0;
  --color-premium-accent: #D4E4D8;
  --color-premium-neutral: #F5F5F0;
  --color-premium-text: #2D3748;
  --color-premium-text-light: #718096;
  --color-premium-border: rgba(124, 152, 133, 0.1);
  --color-premium-shadow: rgba(124, 152, 133, 0.15);
}

/* Premium Utilities */
.invisible-luxury {
  opacity: 0.97;
  filter: brightness(1.02) contrast(1.01);
}

.premium-clickable {
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: transform;
}

.premium-clickable:hover {
  transform: translateY(-1px);
}

.premium-clickable:active {
  transform: translateY(0);
}

.premium-text-balance {
  text-wrap: balance;
}

.premium-text-pretty {
  text-wrap: pretty;
}

/* Premium Layout */
.premium-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 clamp(1rem, 4vw, 2rem);
}

.premium-grid {
  display: grid;
  gap: clamp(1rem, 3vw, 2rem);
}

.premium-flex {
  display: flex;
  gap: clamp(0.5rem, 2vw, 1rem);
}

.premium-spacing {
  padding: clamp(2rem, 8vw, 6rem) 0;
}

.premium-spacing-small {
  padding: clamp(1rem, 4vw, 3rem) 0;
}

.premium-spacing-large {
  padding: clamp(4rem, 12vw, 8rem) 0;
}
